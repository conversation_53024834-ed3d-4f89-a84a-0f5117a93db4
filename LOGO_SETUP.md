# 谷谷Logo设置指南

## 📁 文件放置位置

请将你的 `logo.png` 文件放置在以下位置：

```
protal/
├── public/
│   ├── logo.png  ← 将你的logo文件放在这里
│   ├── file.svg
│   ├── globe.svg
│   └── ...
├── src/
└── ...
```

## 🖼️ Logo要求

- **文件名**: `logo.png`
- **尺寸**: 1024x1024 像素（已优化）
- **格式**: PNG（支持透明背景）
- **建议**: 圆形或方形设计，适合圆角显示

## 🎨 Logo在网站中的使用

你的logo将会显示在以下位置：

### 1. 导航栏 (48x48px)
- 左上角，带有浮动动画
- 圆形显示，带有发光效果
- 悬停时有彩色光环

### 2. 主页Hero区域 (160x160px)
- 页面中央，大尺寸展示
- 圆角矩形显示，带有阴影
- 装饰性光环和闪烁效果

### 3. 页脚 (32x32px)
- 页面底部，小尺寸显示
- 圆形显示

### 4. 网站图标
- 浏览器标签页图标
- 社交媒体分享图片
- PWA应用图标

## 🔄 备用方案

如果 `logo.png` 文件不存在或加载失败，网站会自动显示：
- 🌾 谷子emoji作为默认图标
- 粉色到紫色的渐变背景

## 🚀 如何添加Logo

1. 将你的 `logo.png` 文件复制到 `public/` 目录
2. 确保文件名正确：`logo.png`
3. 重新启动开发服务器：`npm run dev`
4. 访问 http://localhost:3000 查看效果

## 🎯 优化建议

- 使用高质量的PNG格式
- 确保在小尺寸下仍然清晰可见
- 考虑透明背景以适应不同主题
- 保持简洁的设计，符合二次元风格

## 🐛 故障排除

如果logo没有显示：

1. 检查文件路径：`public/logo.png`
2. 检查文件名大小写
3. 清除浏览器缓存
4. 重新启动开发服务器
5. 查看浏览器控制台是否有错误信息
