# iOS Universal Links 配置指南

本项目已经为iOS Universal Links配置了必要的文件和页面。

## 已创建的文件

### 1. Apple App Site Association 文件
- `public/apple-app-site-association` - 主要的AASA文件
- `public/.well-known/apple-app-site-association` - 备用路径的AASA文件

### 2. Universal Links 演示页面
- `src/app/universal-links/page.js` - 展示Universal Links功能的页面

## 配置步骤

### 1. 更新AASA文件中的应用信息

编辑 `public/apple-app-site-association` 和 `public/.well-known/apple-app-site-association` 文件：

```json
{
  "applinks": {
    "details": [
      {
        "appIDs": [
          "YOUR_TEAM_ID.com.yourcompany.protal"
        ],
        "components": [
          {
            "/": "*",
            "comment": "Matches all paths"
          }
        ]
      }
    ]
  }
}
```

**需要替换的内容：**
- `YOUR_TEAM_ID` - 替换为你的Apple Developer Team ID
- `com.yourcompany.protal` - 替换为你的应用Bundle ID

### 2. 在iOS应用中配置Associated Domains

在你的iOS应用的 `Info.plist` 或 Xcode 项目设置中添加：

```xml
<key>com.apple.developer.associated-domains</key>
<array>
    <string>applinks:yourdomain.com</string>
</array>
```

或在Xcode中的Capabilities -> Associated Domains中添加：
```
applinks:yourdomain.com
```

### 3. 部署到服务器

确保AASA文件可以通过以下URL访问：
- `https://yourdomain.com/apple-app-site-association`
- `https://yourdomain.com/.well-known/apple-app-site-association`

**重要要求：**
- 必须使用HTTPS
- 文件必须返回 `application/json` Content-Type
- 文件大小不能超过128KB
- 不能有重定向

### 4. 测试Universal Links

1. 在iOS设备上安装你的应用
2. 访问 `https://yourdomain.com/universal-links` 查看演示页面
3. 点击支持的链接，应该会在应用中打开而不是Safari

### 5. 验证AASA文件

使用Apple的验证工具：
```
https://search.developer.apple.com/appsearch-validation-tool/
```

输入你的域名来验证AASA文件是否正确配置。

## 支持的URL模式

当前配置支持以下URL模式：
- `yourdomain.com/*` - 所有路径
- `yourdomain.com/app/*` - 应用相关路径
- `yourdomain.com/share/*` - 分享相关路径

## 自定义URL Scheme

在Universal Links不可用时，应用还应该支持自定义URL Scheme作为备选方案：
```
protal://
```

## 故障排除

1. **链接不在应用中打开**
   - 检查AASA文件是否可以通过HTTPS访问
   - 验证Team ID和Bundle ID是否正确
   - 确保应用已正确配置Associated Domains

2. **AASA文件验证失败**
   - 检查JSON格式是否正确
   - 确保Content-Type为application/json
   - 检查文件大小是否超过128KB

3. **首次安装后不工作**
   - iOS会在应用安装时下载AASA文件
   - 如果安装时网络不可用，可能需要重新安装应用

## 更多资源

- [Apple Universal Links 官方文档](https://developer.apple.com/ios/universal-links/)
- [AASA 文件格式说明](https://developer.apple.com/documentation/bundleresources/applinks)
