/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',  // ✅ 新方式替代 next export

  // 静态导出时需要禁用图片优化
  images: {
    unoptimized: true,
  },

  // 配置静态文件处理，确保apple-app-site-association文件正确输出
  async headers() {
    return [
      {
        source: '/apple-app-site-association',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/json',
          },
        ],
      },
      {
        source: '/.well-known/apple-app-site-association',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/json',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
