/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',  // ✅ 新方式替代 next export

  // 静态导出时需要禁用图片优化
  images: {
    unoptimized: true,
  },

  // 配置 rewrites 以支持 .well-known 文件访问
  async rewrites() {
    return [
      {
        source: '/.well-known/apple-app-site-association',
        destination: '/api/apple-app-site-association',
      },
    ];
  },
};

export default nextConfig;
