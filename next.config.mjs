/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',  // ✅ 新方式替代 next export

  // 静态导出时需要禁用图片优化
  images: {
    unoptimized: true,
  },

  // 确保 .well-known 目录被复制到输出目录
  trailingSlash: true,

  // 自定义 webpack 配置以复制 .well-known 目录
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // 使用 copy-webpack-plugin 复制 .well-known 目录
      const CopyPlugin = require('copy-webpack-plugin');
      config.plugins.push(
        new CopyPlugin({
          patterns: [
            {
              from: 'public/.well-known',
              to: '.well-known',
            },
          ],
        })
      );
    }
    return config;
  },
};

export default nextConfig;
