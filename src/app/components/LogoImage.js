'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

export default function LogoImage({
  width = 48,
  height = 48,
  className = "",
  priority = false,
  showFallback = true
}) {
  const [imageError, setImageError] = useState(false);

  const handleImageError = (error) => {
    console.error('Logo image failed to load: /logo.png', error);
    console.log('Current environment:', process.env.NODE_ENV);
    console.log('Image component props:', { width, height, className, priority });
    setImageError(true);
  };

  // 为小尺寸logo提供更高的质量设置
  const isSmallLogo = width <= 48;
  const logoQuality = isSmallLogo ? 100 : 95;

  useEffect(() => {
    // 在开发环境下检查logo文件是否存在
    if (process.env.NODE_ENV === 'development') {
      fetch('/logo.png')
        .then(response => {
          if (!response.ok) {
            console.warn('Logo file not found at /logo.png. Please ensure the file exists in the public directory.');
          }
        })
        .catch(() => {
          console.warn('Unable to verify logo file existence.');
        });
    }
  }, []);

  // 如果图片加载失败，显示可爱的默认图标
  if (imageError && showFallback) {
    return (
      <div 
        className={`flex items-center justify-center bg-gradient-to-br from-pink-300 to-purple-300 text-white font-bold text-xl ${className}`}
        style={{ width, height }}
      >
        🌾
      </div>
    );
  }

  // 在静态导出模式下，使用普通的 img 标签更可靠
  if (process.env.NODE_ENV === 'production') {
    return (
      <img
        src="/logo.png"
        alt="谷谷Logo"
        className={`logo-hd ${className}`}
        onError={handleImageError}
        style={{
          width: `${width}px`,
          height: `${height}px`,
          objectFit: 'cover'
        }}
      />
    );
  }

  // 开发环境使用 Next.js Image 组件
  return (
    <Image
      src="/logo.png"
      alt="谷谷Logo"
      width={width}
      height={height}
      className={`logo-hd ${className}`}
      priority={priority}
      quality={logoQuality}
      unoptimized={true}
      sizes={`(max-width: 768px) ${width}px, ${width * 2}px`}
      onError={handleImageError}
      style={{
        width: `${width}px`,
        height: `${height}px`,
        objectFit: 'cover'
      }}
    />
  );
}
