'use client';

import { useEffect } from 'react';

export default function PerformanceOptimizer() {
  useEffect(() => {
    // 检测设备性能并调整动画
    const checkPerformance = () => {
      // 检测是否为低性能设备
      const isLowPerformance = 
        navigator.hardwareConcurrency <= 2 || 
        navigator.deviceMemory <= 2 ||
        /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      if (isLowPerformance) {
        // 为低性能设备添加类名以减少动画
        document.documentElement.classList.add('low-performance');
      }

      // 检测用户是否偏好减少动画
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      if (prefersReducedMotion) {
        document.documentElement.classList.add('reduced-motion');
      }
    };

    // 优化滚动性能
    const optimizeScroll = () => {
      let ticking = false;
      
      const updateScrollElements = () => {
        // 这里可以添加滚动时的优化逻辑
        ticking = false;
      };

      const onScroll = () => {
        if (!ticking) {
          requestAnimationFrame(updateScrollElements);
          ticking = true;
        }
      };

      window.addEventListener('scroll', onScroll, { passive: true });
      
      return () => {
        window.removeEventListener('scroll', onScroll);
      };
    };

    checkPerformance();
    const cleanupScroll = optimizeScroll();

    return cleanupScroll;
  }, []);

  return null; // 这是一个无UI的优化组件
}
