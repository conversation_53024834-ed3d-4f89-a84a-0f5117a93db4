@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700;800&display=swap');

:root {
  --primary: #FF6EB3;
  --primary-light: #FFB3D9;
  --primary-dark: #E55A9F;
  --secondary: #6B73FF;
  --accent: #FFD93D;
  --background: #FFF8FC;
  --foreground: #2D1B3D;
  --card-bg: rgba(255, 255, 255, 0.9);
  --gradient-1: linear-gradient(135deg, #FF6EB3 0%, #6B73FF 100%);
  --gradient-2: linear-gradient(45deg, #FFD93D 0%, #FF6EB3 50%, #6B73FF 100%);
}

/* 使用标准CSS变量替代@theme */
/* @theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --font-sans: 'Nunito', var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
} */

/* 夜间模式已禁用 - 始终使用亮色主题 */
/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #1A0B2E;
    --foreground: #F8E8FF;
    --card-bg: rgba(45, 27, 61, 0.9);
  }
} */

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Nunito', Arial, Helvetica, sans-serif;
  overflow-x: hidden;
  /* 启用硬件加速 */
  transform: translate3d(0, 0, 0);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 二次元风格动画 - GPU加速优化 */
@keyframes float {
  0%, 100% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(0, -20px, 0);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: translate3d(0, 0, 0) scale(0);
  }
  50% {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
  }
}

@keyframes bounce-in {
  0% {
    transform: translate3d(0, 0, 0) scale(0.3) rotate(-10deg);
    opacity: 0;
  }
  50% {
    transform: translate3d(0, 0, 0) scale(1.05) rotate(5deg);
  }
  70% {
    transform: translate3d(0, 0, 0) scale(0.9) rotate(-2deg);
  }
  100% {
    transform: translate3d(0, 0, 0) scale(1) rotate(0deg);
    opacity: 1;
  }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* 动画类 - 添加GPU加速 */
.anime-float {
  animation: float 3s ease-in-out infinite;
  will-change: transform;
  transform: translate3d(0, 0, 0);
}

.anime-sparkle {
  animation: sparkle 2s ease-in-out infinite;
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0);
}

.anime-bounce-in {
  animation: bounce-in 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0);
}

.anime-fade-in-up {
  animation: fade-in-up 0.6s ease-out forwards;
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0);
}

.gradient-text {
  background: var(--gradient-1);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease infinite;
  will-change: background-position;
}

.glass-card {
  background: var(--card-bg);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 110, 179, 0.2);
  box-shadow: 0 8px 32px rgba(255, 110, 179, 0.1);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.kawaii-shadow {
  box-shadow: 0 10px 30px rgba(255, 110, 179, 0.3);
}

/* 性能优化类 */
.gpu-accelerated {
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

/* 减少重绘的悬停效果 */
.hover-scale {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.hover-scale:hover {
  transform: translate3d(0, 0, 0) scale(1.05);
}

/* 响应用户的动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .anime-float,
  .anime-sparkle,
  .anime-bounce-in,
  .anime-fade-in-up {
    animation: none !important;
  }

  .gradient-text {
    animation: none !important;
  }
}

/* 优化滚动性能 */
* {
  scroll-behavior: smooth;
}

/* 滚动目标区域优化 */
.scroll-mt-20 {
  scroll-margin-top: 5rem;
}

/* 滚动指示器动画 */
@keyframes scroll-highlight {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 110, 179, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 110, 179, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 110, 179, 0);
  }
}

.scroll-target {
  animation: scroll-highlight 2s ease-out;
}

/* 弹窗样式优化 */
.modal-backdrop {
  backdrop-filter: blur(8px);
  background: rgba(0, 0, 0, 0.5);
}

/* 模态框样式 - 标准实现 */
.modal-overlay {
  /* 全屏遮罩 */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 50;
  /* 只负责水平居中 */
  display: flex;
  justify-content: center;
  padding: 2rem;
}

.modal-dialog {
  /* 弹窗容器 - 使用top定位 */
  position: relative;
  top: 50%;
  width: 100%;
  max-width: 28rem;
  max-height: 400px;
  /* 确保内容从顶部开始布局 */
  display: flex;
  flex-direction: column;
}

/* 表单样式优化 */
.form-input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 110, 179, 0.15);
}

/* 按钮悬停效果 */
.contact-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.contact-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 110, 179, 0.25);
}

/* 减少重绘的优化 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 低性能设备优化 */
.low-performance .anime-float,
.low-performance .anime-sparkle,
.low-performance .gradient-text {
  animation: none !important;
}

.low-performance .glass-card {
  backdrop-filter: none !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

.low-performance .hover-scale:hover {
  transform: none !important;
}

/* 减少动画偏好设置 */
.reduced-motion * {
  animation: none !important;
  transition: none !important;
}

/* Logo特殊样式 */
.logo-glow {
  filter: drop-shadow(0 0 20px rgba(255, 110, 179, 0.3));
  transition: filter 0.3s ease;
  /* 确保图片清晰度 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: pixelated;
  image-rendering: auto;
}

.logo-glow:hover {
  filter: drop-shadow(0 0 30px rgba(255, 110, 179, 0.5));
}

.logo-ring {
  position: relative;
}

.logo-ring::before {
  content: '';
  position: absolute;
  inset: -8px;
  border: 2px solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, #FF6EB3, #6B73FF, #FFD93D) border-box;
  mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.logo-ring:hover::before {
  opacity: 1;
}

/* 高清logo优化 */
.logo-hd {
  /* 确保在高DPI屏幕上清晰显示 */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  /* 优化渲染性能 */
  will-change: transform;

  /* 确保图片不被压缩 */
  max-width: none;
  height: auto;
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .logo-hd {
    /* 在高DPI屏幕上使用更平滑的渲染 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: auto;
  }
}

/* 标准屏幕优化 */
@media (-webkit-max-device-pixel-ratio: 1.5), (max-resolution: 144dpi) {
  .logo-hd {
    /* 在标准屏幕上保持像素完美 */
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
  }
}

/* 手机界面样式 */
.phone-mockup {
  background: linear-gradient(145deg, #f0f0f0, #ffffff);
  box-shadow:
    inset 0 2px 4px rgba(255, 255, 255, 0.8),
    inset 0 -2px 4px rgba(0, 0, 0, 0.1),
    0 8px 32px rgba(255, 110, 179, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.phone-mockup::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  border-radius: inherit;
  pointer-events: none;
}

/* 行情价动画效果 */
@keyframes price-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes trend-up {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
  100% { transform: translateY(0px); }
}

@keyframes trend-down {
  0% { transform: translateY(0px); }
  50% { transform: translateY(3px); }
  100% { transform: translateY(0px); }
}

.price-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.price-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.price-up {
  animation: trend-up 2s ease-in-out infinite;
}

.price-down {
  animation: trend-down 2s ease-in-out infinite;
}

.price-highlight {
  animation: price-pulse 3s ease-in-out infinite;
}
