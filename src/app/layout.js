import { Geist, <PERSON><PERSON><PERSON>_Mono, <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const nunito = Nunito({
  variable: "--font-nunito",
  subsets: ["latin"],
  weight: ["300", "400", "600", "700", "800"],
});

export const metadata = {
  title: "谷谷 - 谷子管理应用",
  description: "谷谷是一个高效实用的谷子管理应用，集成了内容发布、私信等社交功能，让你的谷子生活更加精彩！",
  keywords: "谷谷,谷子管理,社交,App,管理工具",
  authors: [{ name: "武汉菁耀科技有限公司" }],
  icons: {
    icon: '/logo.png',
    shortcut: '/logo.png',
    apple: '/logo.png',
  },
  openGraph: {
    title: "谷谷 - 谷子管理应用",
    description: "谷谷是一个高效实用的谷子管理应用",
    type: "website",
    images: [
      {
        url: '/logo.png',
        width: 1024,
        height: 1024,
        alt: '谷谷Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "谷谷 - 谷子管理应用",
    description: "谷谷是一个高效实用的谷子管理应用",
    images: ['/logo.png'],
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="zh-CN">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${nunito.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
