'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import PerformanceOptimizer from './components/PerformanceOptimizer';
import LogoImage from './components/LogoImage';
import Modal from './components/Modal';
import ContactUs from './components/ContactUs';

export default function Home() {
  const [isVisible, setIsVisible] = useState(false);
  const [showContactModal, setShowContactModal] = useState(false);

  useEffect(() => {
    // 使用requestAnimationFrame确保动画在下一帧开始
    const timer = requestAnimationFrame(() => {
      setIsVisible(true);
    });

    return () => cancelAnimationFrame(timer);
  }, []);

  // 处理"了解更多"按钮点击
  const handleLearnMore = () => {
    const featuresSection = document.getElementById('features');
    if (featuresSection) {
      // 平滑滚动到功能特色区域
      featuresSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });

      // 添加短暂的视觉高亮效果
      setTimeout(() => {
        featuresSection.classList.add('scroll-target');
        setTimeout(() => {
          featuresSection.classList.remove('scroll-target');
        }, 2000);
      }, 500);
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      <PerformanceOptimizer />
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50"></div>

      {/* 浮动装饰元素 - 优化GPU加速 */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-pink-300 to-purple-300 rounded-full opacity-20 anime-float gpu-accelerated"></div>
      <div className="absolute top-40 right-20 w-16 h-16 bg-gradient-to-r from-yellow-300 to-pink-300 rounded-full opacity-20 anime-float gpu-accelerated" style={{animationDelay: '1s'}}></div>
      <div className="absolute bottom-40 left-20 w-12 h-12 bg-gradient-to-r from-blue-300 to-purple-300 rounded-full opacity-20 anime-float gpu-accelerated" style={{animationDelay: '2s'}}></div>
      <div className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-pink-300 to-yellow-300 rounded-full opacity-20 anime-float gpu-accelerated" style={{animationDelay: '0.5s'}}></div>

      {/* 导航栏 */}
      <nav className="relative z-10 p-6">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className={`flex items-center space-x-3 ${isVisible ? 'anime-bounce-in' : 'opacity-0'}`}>
            <div className="relative w-12 h-12 anime-float gpu-accelerated logo-ring">
              <LogoImage
                width={48}
                height={48}
                className="rounded-full object-cover logo-glow"
                priority
              />
            </div>
            <div className="text-3xl font-bold gradient-text">
              谷谷
            </div>
          </div>
          <div className="hidden md:flex space-x-8">
            <a href="#features" className="text-gray-700 hover:text-[#FF6EB3] transition-colors font-medium">功能特色</a>
            <Link href="/support" className="text-gray-700 hover:text-[#FF6EB3] transition-colors font-medium">技术支持</Link>
            <a href="#download" className="text-gray-700 hover:text-[#FF6EB3] transition-colors font-medium">下载</a>
          </div>
        </div>
      </nav>

      {/* 主要内容区域 */}
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="px-6 py-20">
          <div className="max-w-7xl mx-auto text-center">
            <div className={`gpu-accelerated ${isVisible ? 'anime-fade-in-up' : 'opacity-0'}`} style={{animationDelay: '0.1s'}}>
              <h1 className="text-6xl md:text-8xl font-bold mb-8">
                <span className="gradient-text">谷谷</span>
              </h1>
              <p className="text-lg text-gray-500 mb-12 max-w-2xl mx-auto">
                一个高效实用的谷子管理应用，集成了内容发布、私信等社交功能，让你的谷子生活更加精彩！
              </p>
            </div>

            {/* App预览卡片 */}
            <div className={`glass-card rounded-3xl p-8 max-w-4xl mx-auto mb-16 gpu-accelerated ${isVisible ? 'anime-fade-in-up' : 'opacity-0'}`} style={{animationDelay: '0.3s'}}>
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="text-left">
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    🌾 管理你的谷子
                  </h2>
                  <p className="text-gray-600 mb-6">
                    轻松记录和管理你的谷子收藏，支持分类、标签、搜索等功能，还能一键查看价格信息，让你的谷子井井有条。
                  </p>
                  <div className="flex flex-wrap gap-3">
                    <span className="px-4 py-2 bg-pink-100 text-pink-600 rounded-full text-sm font-medium">谷子管理</span>
                    <span className="px-4 py-2 bg-green-100 text-green-600 rounded-full text-sm font-medium">价格查询</span>
                    <span className="px-4 py-2 bg-purple-100 text-purple-600 rounded-full text-sm font-medium">智能分类</span>
                    <span className="px-4 py-2 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">快速搜索</span>
                  </div>
                </div>
                <div className="relative">
                  {/* 手机框架 */}
                  <div className="w-64 h-64 mx-auto phone-mockup rounded-3xl flex flex-col items-center justify-center anime-float gpu-accelerated p-6 relative overflow-hidden">
                    {/* 手机状态栏模拟 */}
                    <div className="absolute top-3 left-4 right-4 flex justify-between items-center text-xs text-gray-500">
                      <div className="flex items-center space-x-1">
                        <div className="flex space-x-0.5">
                          <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
                          <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
                          <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
                        </div>
                        <span className="ml-1 text-xs">谷谷</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-4 h-2 border border-gray-500 rounded-sm">
                          <div className="w-full h-full bg-green-400 rounded-sm"></div>
                        </div>
                      </div>
                    </div>

                    {/* App图标区域 */}
                    <div className="flex-1 flex flex-col items-center justify-center mt-4">
                      <div className="relative w-20 h-20 logo-ring mb-3">
                        <LogoImage
                          width={80}
                          height={80}
                          className="rounded-2xl object-cover kawaii-shadow logo-glow"
                        />
                        {/* 装饰性闪烁点 */}
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-300 rounded-full opacity-80 anime-sparkle"></div>
                        <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-pink-300 rounded-full opacity-80 anime-sparkle" style={{animationDelay: '0.5s'}}></div>
                      </div>

                      {/* App名称 */}
                      <div className="text-center">
                        <div className="text-sm font-bold text-gray-700 mb-1">谷谷</div>
                        <div className="text-xs text-gray-500">谷子管理助手</div>
                      </div>
                    </div>

                    {/* 底部装饰 */}
                    <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gray-300 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* CTA按钮 */}
            <div className={`flex flex-col sm:flex-row gap-4 justify-center gpu-accelerated ${isVisible ? 'anime-fade-in-up' : 'opacity-0'}`} style={{animationDelay: '0.5s'}}>
              <button className="px-8 py-4 bg-gradient-to-r from-[#FF6EB3] to-[#6B73FF] text-white rounded-full font-bold text-lg kawaii-shadow hover-scale">
                立即下载 ✨
              </button>
              <button
                onClick={handleLearnMore}
                className="px-8 py-4 glass-card text-gray-700 rounded-full font-bold text-lg hover-scale"
              >
                了解更多 💫
              </button>
            </div>
          </div>
        </section>



        {/* 功能特色区域 */}
        <section id="features" className="px-6 py-20 bg-white/50 scroll-mt-20">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold gradient-text mb-4">功能特色</h2>
              <p className="text-lg text-gray-600">探索谷谷的强大功能，让谷子管理变得更加高效</p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* 功能卡片1 */}
              <div className="glass-card rounded-2xl p-6 text-center hover-scale gpu-accelerated">
                <div className="text-5xl mb-4">🌾</div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">谷子管理</h3>
                <p className="text-gray-600">智能分类和标签系统，让你的谷子收藏井井有条</p>
              </div>

              {/* 功能卡片2 - 价格查询功能 */}
              <div className="glass-card rounded-2xl p-6 text-center hover-scale gpu-accelerated">
                <div className="text-5xl mb-4">📈</div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">价格查询</h3>
                <p className="text-gray-600">一键查看谷子价格信息，方便管理和决策</p>
              </div>

              {/* 功能卡片3 */}
              <div className="glass-card rounded-2xl p-6 text-center hover-scale gpu-accelerated">
                <div className="text-5xl mb-4">📝</div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">内容发布</h3>
                <p className="text-gray-600">分享你的谷子心得，与其他用户交流经验</p>
              </div>

              {/* 功能卡片4 */}
              <div className="glass-card rounded-2xl p-6 text-center hover-scale gpu-accelerated">
                <div className="text-5xl mb-4">💬</div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">私信聊天</h3>
                <p className="text-gray-600">与好友私信交流，建立你的谷子社交圈</p>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* 页脚 */}
      <footer className="relative z-10 bg-white/80 backdrop-blur-sm py-12">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="relative w-8 h-8">
              <LogoImage
                width={32}
                height={32}
                className="rounded-full object-cover"
              />
            </div>
            <div className="text-2xl font-bold gradient-text">谷谷</div>
          </div>
          <p className="text-gray-600 mb-6">让谷子管理变得更加有趣 ✨</p>
          <div className="flex justify-center space-x-8 text-gray-500">
            <Link
              href="/support"
              className="hover:text-[#FF6EB3] transition-colors"
            >
              技术支持
            </Link>
            <Link
              href="/privacy"
              className="hover:text-[#FF6EB3] transition-colors"
            >
              隐私政策
            </Link>
            <button
              onClick={() => setShowContactModal(true)}
              className="hover:text-[#FF6EB3] transition-colors"
            >
              联系我们
            </button>
          </div>
          <div className="mt-8 text-sm text-gray-400 space-y-2">
            <div>© 2025 武汉菁耀科技有限公司. All rights reserved.</div>
            <div>
              <a
                href="https://beian.miit.gov.cn"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-[#FF6EB3] transition-colors"
              >
                鄂ICP备2023006067号-1
              </a>
            </div>
          </div>
        </div>
      </footer>

      {/* 联系我们弹窗 */}
      <Modal
        isOpen={showContactModal}
        onClose={() => setShowContactModal(false)}
        title="联系我们"
      >
        <ContactUs />
      </Modal>
    </div>
  );
}
