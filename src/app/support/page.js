import Link from 'next/link';
import LogoImage from '../components/LogoImage';

export const metadata = {
  title: "技术支持 - 谷谷",
  description: "谷谷应用技术支持页面，获取帮助、常见问题解答和联系客服。",
};

export default function SupportPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50">
      {/* 导航栏 */}
      <nav className="relative z-10 p-6 bg-white/80 backdrop-blur-sm">
        <div className="max-w-4xl mx-auto flex justify-between items-center">
          <Link href="/" className="flex items-center space-x-3">
            <div className="relative w-10 h-10">
              <LogoImage
                width={40}
                height={40}
                className="rounded-full object-cover"
              />
            </div>
            <div className="text-2xl font-bold gradient-text">谷谷</div>
          </Link>
          <Link 
            href="/"
            className="px-6 py-2 glass-card text-gray-700 rounded-full font-medium hover-scale"
          >
            返回首页
          </Link>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-4xl mx-auto px-6 py-12">
        <div className="glass-card rounded-3xl p-8 md:p-12">
          {/* 页面标题 */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold gradient-text mb-4">技术支持</h1>
            <p className="text-gray-600">我们随时为您提供帮助和支持</p>
          </div>

          {/* 快速联系 */}
          <section className="mb-12">
            <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl p-8 text-center">
              <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center justify-center">
                <span className="text-2xl mr-3">📞</span>
                需要帮助？立即联系我们
              </h2>
              <p className="text-gray-600 mb-6">我们的技术支持团队随时为您解答问题</p>
              <a 
                href="mailto:<EMAIL>" 
                className="inline-block px-8 py-4 bg-gradient-to-r from-[#FF6EB3] to-[#6B73FF] text-white rounded-xl font-bold hover-scale"
              >
                📧 发送邮件获取支持
              </a>
              <p className="text-sm text-gray-500 mt-4">通常在24小时内回复</p>
            </div>
          </section>

          {/* 常见问题 */}
          <section className="mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center flex items-center justify-center">
              <span className="text-3xl mr-3">❓</span>
              常见问题
            </h2>
            
            <div className="space-y-6">
              {/* 账户相关 */}
              <div className="bg-blue-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <span className="mr-3">👤</span>
                  账户相关
                </h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">如何注册谷谷账户？</h4>
                    <p className="text-gray-600 text-sm">下载谷谷App后，点击&ldquo;注册&rdquo;按钮，使用手机号即可快速注册。</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">忘记密码怎么办？</h4>
                    <p className="text-gray-600 text-sm">在登录页面点击&ldquo;忘记密码&rdquo;，输入注册手机号，我们会发送验证码给您。</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">如何修改个人信息？</h4>
                    <p className="text-gray-600 text-sm">登录后进入&ldquo;我的 &gt; 点击头像&rdquo;，可以修改昵称、头像等信息。</p>
                  </div>
                </div>
              </div>

              {/* 功能使用 */}
              <div className="bg-green-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <span className="mr-3">🌾</span>
                  功能使用
                </h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">如何创建和管理谷子？</h4>
                    <p className="text-gray-600 text-sm">点击首页的&ldquo;+&rdquo;按钮，填写谷子信息，可以添加图片、标签和描述。在&ldquo;我的谷子&rdquo;中可以查看和管理。</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">如何与其他用户互动？</h4>
                    <p className="text-gray-600 text-sm">您可以关注其他用户、点赞评论他们的发布的内容，也可以通过私信功能进行交流。</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">如何查看价格信息？</h4>
                    <p className="text-gray-600 text-sm">在谷子详情页面点击&ldquo;一键估值&rdquo;按钮，可以查看相关的市场价格信息。</p>
                  </div>
                </div>
              </div>

              {/* 技术问题 */}
              <div className="bg-purple-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <span className="mr-3">🔧</span>
                  技术问题
                </h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">App无法正常启动怎么办？</h4>
                    <p className="text-gray-600 text-sm">请尝试重启App或重启设备。如果问题持续，请确保您使用的是最新版本的App。</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">图片上传失败怎么办？</h4>
                    <p className="text-gray-600 text-sm">请检查网络连接，确保图片大小不超过5MB，格式为JPG或PNG。</p>
                  </div>
                </div>
              </div>

              {/* 隐私安全 */}
              <div className="bg-orange-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <span className="mr-3">🔒</span>
                  隐私与安全
                </h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">我的数据安全吗？</h4>
                    <p className="text-gray-600 text-sm">我们采用行业标准的加密技术保护您的数据，详情请查看我们的 
                      <Link href="/privacy" className="text-blue-600 hover:underline">隐私政策</Link>。
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">如何删除我的账户？</h4>
                    <p className="text-gray-600 text-sm">在设置中选择&ldquo;删除账户&rdquo;。</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">如何举报不当内容？</h4>
                    <p className="text-gray-600 text-sm">点击内容右上角的&ldquo;...&rdquo;按钮，选择&ldquo;举报&rdquo;，我们会及时处理。</p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* 系统要求 */}
          <section className="mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center flex items-center justify-center">
              <span className="text-3xl mr-3">📱</span>
              系统要求
            </h2>
            
            <div className="max-w-md mx-auto">
              <div className="bg-blue-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center justify-center">
                  <span className="mr-3">🍎</span>
                  iOS 设备要求
                </h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• iOS 12.0 或更高版本</li>
                  <li>• iPhone 6s 或更新机型</li>
                  <li>• 至少 100MB 可用存储空间</li>
                  <li>• 网络连接（Wi-Fi 或蜂窝数据）</li>
                </ul>
              </div>
            </div>
          </section>

          {/* 联系信息 */}
          <section className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl p-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center">
              <span className="text-3xl mr-3">💬</span>
              联系我们
            </h2>
            
            <div className="text-center space-y-6">
              <div className="bg-white rounded-xl p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4">技术支持邮箱</h3>
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-2xl text-blue-600 hover:text-blue-800 transition-colors font-medium"
                >
                  <EMAIL>
                </a>
                <p className="text-gray-500 mt-2">我们会在24小时内回复您的邮件</p>
              </div>

              <div className="bg-white rounded-xl p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4">开发公司</h3>
                <p className="text-lg text-gray-700">武汉菁耀科技有限公司</p>
                <p className="text-gray-500 mt-2">致力于为用户提供优质的移动应用体验</p>
              </div>

              <div className="bg-white rounded-xl p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4">服务时间</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-lg font-bold text-blue-600">24小时</div>
                    <div className="text-sm text-gray-600">邮件回复</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-green-600">工作日</div>
                    <div className="text-sm text-gray-600">9:00-18:00</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-purple-600">7天</div>
                    <div className="text-sm text-gray-600">全周支持</div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </main>

      {/* 页脚 */}
      <footer className="bg-white/80 backdrop-blur-sm py-8 mt-12">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <div className="text-sm text-gray-500 space-y-2">
            <div className="flex justify-center space-x-6">
              <Link href="/" className="hover:text-[#FF6EB3] transition-colors">
                返回首页
              </Link>
              <Link href="/privacy" className="hover:text-[#FF6EB3] transition-colors">
                隐私政策
              </Link>
            </div>
            <div>© 2025 武汉菁耀科技有限公司. All rights reserved.</div>
          </div>
        </div>
      </footer>
    </div>
  );
}
