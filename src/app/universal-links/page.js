'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import PerformanceOptimizer from '../components/PerformanceOptimizer';
import LogoImage from '../components/LogoImage';

export default function UniversalLinksPage() {
  const [isVisible, setIsVisible] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [appInstalled, setAppInstalled] = useState(false);

  useEffect(() => {
    // 页面加载动画
    const timer = requestAnimationFrame(() => {
      setIsVisible(true);
    });

    // 检测是否为iOS设备
    const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    setIsIOS(iOS);

    // 检测应用是否已安装（通过尝试打开应用链接）
    if (iOS) {
      const timeout = setTimeout(() => {
        setAppInstalled(false);
      }, 2000);

      // 如果应用打开成功，页面会失去焦点
      const handleVisibilityChange = () => {
        if (document.hidden) {
          setAppInstalled(true);
          clearTimeout(timeout);
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);

      return () => {
        clearTimeout(timeout);
        cancelAnimationFrame(timer);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    }

    return () => cancelAnimationFrame(timer);
  }, []);

  const handleOpenApp = () => {
    if (isIOS) {
      // 尝试打开应用
      window.location.href = 'protal://';

      // 如果应用未安装，2秒后跳转到App Store
      setTimeout(() => {
        if (!appInstalled) {
          window.location.href = 'https://apps.apple.com/app/your-app-id';
        }
      }, 2000);
    } else {
      alert('此功能仅支持iOS设备');
    }
  };

  const handleDownloadApp = () => {
    window.location.href = 'https://apps.apple.com/app/your-app-id';
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      <PerformanceOptimizer />

      {/* 背景装饰 - 与主页保持一致 */}
      <div className="absolute inset-0 bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50"></div>

      {/* 浮动装饰元素 */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-pink-300 to-purple-300 rounded-full opacity-20 anime-float gpu-accelerated"></div>
      <div className="absolute top-40 right-20 w-16 h-16 bg-gradient-to-r from-yellow-300 to-pink-300 rounded-full opacity-20 anime-float gpu-accelerated" style={{animationDelay: '1s'}}></div>
      <div className="absolute bottom-40 left-20 w-12 h-12 bg-gradient-to-r from-blue-300 to-purple-300 rounded-full opacity-20 anime-float gpu-accelerated" style={{animationDelay: '2s'}}></div>
      <div className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-pink-300 to-yellow-300 rounded-full opacity-20 anime-float gpu-accelerated" style={{animationDelay: '0.5s'}}></div>

      {/* 导航栏 */}
      <nav className="relative z-10 p-6">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <Link href="/" className={`flex items-center space-x-3 ${isVisible ? 'anime-bounce-in' : 'opacity-0'}`}>
            <div className="relative w-12 h-12 anime-float gpu-accelerated logo-ring">
              <LogoImage
                width={48}
                height={48}
                className="rounded-full object-cover logo-glow"
                priority
              />
            </div>
            <div className="text-3xl font-bold gradient-text">
              谷谷
            </div>
          </Link>
          <div className="hidden md:flex space-x-8">
            <Link href="/" className="text-gray-700 hover:text-[#FF6EB3] transition-colors font-medium">首页</Link>
            <Link href="/support" className="text-gray-700 hover:text-[#FF6EB3] transition-colors font-medium">技术支持</Link>
          </div>
        </div>
      </nav>

      {/* 主要内容区域 */}
      <main className="relative z-10 px-6 py-20">
        <div className="max-w-4xl mx-auto">
          {/* 标题区域 */}
          <div className={`text-center mb-16 gpu-accelerated ${isVisible ? 'anime-fade-in-up' : 'opacity-0'}`} style={{animationDelay: '0.1s'}}>
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className="gradient-text">谷谷应用</span>
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              高效实用的谷子管理应用，让你的谷子生活更加精彩 ✨
            </p>
          </div>

          {/* 主要卡片区域 */}
          <div className={`glass-card rounded-3xl p-8 max-w-2xl mx-auto mb-12 gpu-accelerated ${isVisible ? 'anime-fade-in-up' : 'opacity-0'}`} style={{animationDelay: '0.3s'}}>
            <div className="text-center">
              {/* App图标区域 */}
              <div className="mb-8">
                <div className="relative w-24 h-24 mx-auto logo-ring mb-4">
                  <LogoImage
                    width={96}
                    height={96}
                    className="rounded-2xl object-cover kawaii-shadow logo-glow"
                  />
                  {/* 装饰性闪烁点 */}
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-300 rounded-full opacity-80 anime-sparkle"></div>
                  <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-pink-300 rounded-full opacity-80 anime-sparkle" style={{animationDelay: '0.5s'}}></div>
                </div>
                <h2 className="text-3xl font-bold gradient-text mb-2">谷谷</h2>
                <p className="text-gray-600">谷子管理助手</p>
              </div>

              {/* 按钮区域 */}
              <div className="space-y-4">
                <div className="p-4 bg-gradient-to-r from-pink-50 to-purple-50 rounded-2xl border border-pink-200">
                  <div className="text-2xl mb-2">🌟</div>
                  <h3 className="font-bold text-gray-800 mb-1">立即体验谷谷</h3>
                  <p className="text-sm text-gray-600">
                    管理你的谷子收藏，享受便捷的社交功能
                  </p>
                </div>

                <button
                  onClick={handleOpenApp}
                  className="w-full px-8 py-4 bg-gradient-to-r from-[#FF6EB3] to-[#6B73FF] text-white rounded-2xl font-bold text-lg kawaii-shadow hover-scale gpu-accelerated"
                >
                  打开谷谷应用 ✨
                </button>

                <button
                  onClick={handleDownloadApp}
                  className="w-full px-8 py-4 glass-card text-gray-700 rounded-2xl font-bold text-lg hover-scale gpu-accelerated"
                >
                  从 App Store 下载 📱
                </button>
              </div>
            </div>
          </div>

          {/* 功能亮点区域 */}
          <div className={`grid md:grid-cols-3 gap-6 mb-12 gpu-accelerated ${isVisible ? 'anime-fade-in-up' : 'opacity-0'}`} style={{animationDelay: '0.5s'}}>
            <div className="glass-card rounded-2xl p-6 text-center hover-scale gpu-accelerated">
              <div className="text-4xl mb-4">🌾</div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">谷子管理</h3>
              <p className="text-gray-600">
                智能分类和标签系统，让你的谷子收藏井井有条
              </p>
            </div>

            <div className="glass-card rounded-2xl p-6 text-center hover-scale gpu-accelerated">
              <div className="text-4xl mb-4">📝</div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">内容发布</h3>
              <p className="text-gray-600">
                分享你的谷子心得，与其他用户交流经验
              </p>
            </div>

            <div className="glass-card rounded-2xl p-6 text-center hover-scale gpu-accelerated">
              <div className="text-4xl mb-4">💬</div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">私信聊天</h3>
              <p className="text-gray-600">
                与好友私信交流，建立你的谷子社交圈
              </p>
            </div>
          </div>

          {/* 使用说明 */}
          <div className={`glass-card rounded-2xl p-8 text-center gpu-accelerated ${isVisible ? 'anime-fade-in-up' : 'opacity-0'}`} style={{animationDelay: '0.7s'}}>
            <div className="text-4xl mb-4">📲</div>
            <h3 className="text-2xl font-bold gradient-text mb-4">
              如何开始使用？
            </h3>
            <div className="grid md:grid-cols-3 gap-6 text-left">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-pink-400 to-purple-400 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                <div>
                  <h4 className="font-bold text-gray-800 mb-1">下载应用</h4>
                  <p className="text-sm text-gray-600">从App Store下载谷谷应用</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                <div>
                  <h4 className="font-bold text-gray-800 mb-1">创建账户</h4>
                  <p className="text-sm text-gray-600">注册并完善你的个人资料</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                <div>
                  <h4 className="font-bold text-gray-800 mb-1">开始管理</h4>
                  <p className="text-sm text-gray-600">添加你的第一个谷子收藏</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* 页脚 */}
      <footer className="relative z-10 bg-white/80 backdrop-blur-sm py-12">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="relative w-8 h-8">
              <LogoImage
                width={32}
                height={32}
                className="rounded-full object-cover"
              />
            </div>
            <div className="text-2xl font-bold gradient-text">谷谷</div>
          </div>
          <p className="text-gray-600 mb-6">让谷子管理变得更加有趣 ✨</p>
          <div className="flex justify-center space-x-8 text-gray-500">
            <Link
              href="/"
              className="hover:text-[#FF6EB3] transition-colors"
            >
              返回首页
            </Link>
            <Link
              href="/support"
              className="hover:text-[#FF6EB3] transition-colors"
            >
              技术支持
            </Link>
            <Link
              href="/privacy"
              className="hover:text-[#FF6EB3] transition-colors"
            >
              隐私政策
            </Link>
          </div>
          <div className="mt-8 text-sm text-gray-400 space-y-2">
            <div>© 2025 武汉菁耀科技有限公司. All rights reserved.</div>
            <div>
              <a
                href="https://beian.miit.gov.cn"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-[#FF6EB3] transition-colors"
              >
                鄂ICP备2023006067号-1
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
