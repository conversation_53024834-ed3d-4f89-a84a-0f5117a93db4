'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import PerformanceOptimizer from '../components/PerformanceOptimizer';
import LogoImage from '../components/LogoImage';

export default function UniversalLinksPage() {
  const [isVisible, setIsVisible] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [appInstalled, setAppInstalled] = useState(false);

  useEffect(() => {
    // 页面加载动画
    const timer = requestAnimationFrame(() => {
      setIsVisible(true);
    });

    // 检测是否为iOS设备
    const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    setIsIOS(iOS);

    // 检测应用是否已安装（通过尝试打开应用链接）
    if (iOS) {
      const timeout = setTimeout(() => {
        setAppInstalled(false);
      }, 2000);

      // 如果应用打开成功，页面会失去焦点
      const handleVisibilityChange = () => {
        if (document.hidden) {
          setAppInstalled(true);
          clearTimeout(timeout);
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);

      return () => {
        clearTimeout(timeout);
        cancelAnimationFrame(timer);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    }

    return () => cancelAnimationFrame(timer);
  }, []);

  const handleOpenApp = () => {
    if (isIOS) {
      // 尝试打开应用
      window.location.href = 'protal://';

      // 如果应用未安装，2秒后跳转到App Store
      setTimeout(() => {
        if (!appInstalled) {
          window.location.href = 'https://apps.apple.com/cn/app/%E8%B0%B7%E8%B0%B7/id6747290272';
        }
      }, 2000);
    } else {
      alert('此功能仅支持iOS设备');
    }
  };

  const handleDownloadApp = () => {
    window.location.href = 'https://apps.apple.com/cn/app/%E8%B0%B7%E8%B0%B7/id6747290272';
  };

  return (
    <div className="min-h-screen relative overflow-hidden flex flex-col">
      <PerformanceOptimizer />

      {/* 背景装饰 - 与主页保持一致 */}
      <div className="absolute inset-0 bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50"></div>

      {/* 浮动装饰元素 */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-pink-300 to-purple-300 rounded-full opacity-20 anime-float gpu-accelerated"></div>
      <div className="absolute top-40 right-20 w-16 h-16 bg-gradient-to-r from-yellow-300 to-pink-300 rounded-full opacity-20 anime-float gpu-accelerated" style={{animationDelay: '1s'}}></div>
      <div className="absolute bottom-40 left-20 w-12 h-12 bg-gradient-to-r from-blue-300 to-purple-300 rounded-full opacity-20 anime-float gpu-accelerated" style={{animationDelay: '2s'}}></div>
      <div className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-pink-300 to-yellow-300 rounded-full opacity-20 anime-float gpu-accelerated" style={{animationDelay: '0.5s'}}></div>

      {/* 导航栏 */}
      <nav className="relative z-10 p-6">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <Link href="/" className={`flex items-center space-x-3 ${isVisible ? 'anime-bounce-in' : 'opacity-0'}`}>
            <div className="relative w-12 h-12 anime-float gpu-accelerated logo-ring">
              <LogoImage
                width={48}
                height={48}
                className="rounded-full object-cover logo-glow"
                priority
              />
            </div>
            <div className="text-3xl font-bold gradient-text">
              谷谷
            </div>
          </Link>
          <div className="hidden md:flex space-x-8">
            <Link href="/" className="text-gray-700 hover:text-[#FF6EB3] transition-colors font-medium">首页</Link>
            <Link href="/support" className="text-gray-700 hover:text-[#FF6EB3] transition-colors font-medium">技术支持</Link>
          </div>
        </div>
      </nav>

      {/* 主要内容区域 - 单屏设计 */}
      <main className="relative z-10 flex-1 flex items-center justify-center px-6">
        <div className="max-w-md mx-auto text-center">
          {/* App图标和标题 */}
          <div className={`mb-8 gpu-accelerated ${isVisible ? 'anime-fade-in-up' : 'opacity-0'}`} style={{animationDelay: '0.1s'}}>
            <div className="relative w-24 h-24 mx-auto logo-ring mb-6">
              <LogoImage
                width={96}
                height={96}
                className="rounded-2xl object-cover kawaii-shadow logo-glow"
              />
              {/* 装饰性闪烁点 */}
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-300 rounded-full opacity-80 anime-sparkle"></div>
              <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-pink-300 rounded-full opacity-80 anime-sparkle" style={{animationDelay: '0.5s'}}></div>
            </div>
            <h1 className="text-4xl font-bold gradient-text mb-3">谷谷</h1>
            <p className="text-gray-600 mb-8">
              高效实用的谷子管理应用<br/>
              让你的谷子生活更加精彩 ✨
            </p>
          </div>

          {/* 按钮区域 */}
          <div className={`space-y-4 gpu-accelerated ${isVisible ? 'anime-fade-in-up' : 'opacity-0'}`} style={{animationDelay: '0.3s'}}>
            <button
              onClick={handleOpenApp}
              className="w-full px-8 py-4 bg-gradient-to-r from-[#FF6EB3] to-[#6B73FF] text-white rounded-2xl font-bold text-lg kawaii-shadow hover-scale gpu-accelerated"
            >
              打开谷谷应用 ✨
            </button>

            <button
              onClick={handleDownloadApp}
              className="w-full px-8 py-4 glass-card text-gray-700 rounded-2xl font-bold text-lg hover-scale gpu-accelerated"
            >
              从 App Store 下载 📱
            </button>
          </div>
        </div>
      </main>


    </div>
  );
}
