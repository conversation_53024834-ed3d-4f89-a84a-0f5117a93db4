'use client';

import { useEffect, useState } from 'react';

export default function UniversalLinksPage() {
  const [isIOS, setIsIOS] = useState(false);
  const [appInstalled, setAppInstalled] = useState(false);

  useEffect(() => {
    // 检测是否为iOS设备
    const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    setIsIOS(iOS);

    // 检测应用是否已安装（通过尝试打开应用链接）
    if (iOS) {
      const timeout = setTimeout(() => {
        setAppInstalled(false);
      }, 2000);

      // 尝试打开应用
      window.location.href = 'protal://';
      
      // 如果应用打开成功，页面会失去焦点
      const handleVisibilityChange = () => {
        if (document.hidden) {
          setAppInstalled(true);
          clearTimeout(timeout);
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);
      
      return () => {
        clearTimeout(timeout);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    }
  }, []);

  const handleOpenApp = () => {
    if (isIOS) {
      // 尝试打开应用
      window.location.href = 'protal://';
      
      // 如果应用未安装，2秒后跳转到App Store
      setTimeout(() => {
        if (!appInstalled) {
          window.location.href = 'https://apps.apple.com/app/your-app-id';
        }
      }, 2000);
    } else {
      alert('此功能仅支持iOS设备');
    }
  };

  const handleDownloadApp = () => {
    window.location.href = 'https://apps.apple.com/app/your-app-id';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
        <div className="mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Protal App</h1>
          <p className="text-gray-600">在移动端获得更好的体验</p>
        </div>

        {isIOS ? (
          <div className="space-y-4">
            <button
              onClick={handleOpenApp}
              className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 px-6 rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 shadow-lg"
            >
              打开应用
            </button>
            
            <button
              onClick={handleDownloadApp}
              className="w-full border-2 border-gray-300 text-gray-700 py-3 px-6 rounded-xl font-semibold hover:border-gray-400 hover:bg-gray-50 transition-all duration-200"
            >
              从 App Store 下载
            </button>

            <div className="mt-8 p-4 bg-blue-50 rounded-xl">
              <h3 className="font-semibold text-blue-900 mb-2">Universal Links 已配置</h3>
              <p className="text-sm text-blue-700">
                当您点击支持的链接时，会自动在应用中打开，无需手动选择。
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="p-4 bg-yellow-50 rounded-xl">
              <p className="text-yellow-800">
                请在iOS设备上访问此页面以体验Universal Links功能
              </p>
            </div>
            
            <button
              onClick={() => window.open('https://apps.apple.com/app/your-app-id', '_blank')}
              className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 px-6 rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 shadow-lg"
            >
              在 App Store 中查看
            </button>
          </div>
        )}

        <div className="mt-8 pt-6 border-t border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-3">支持的链接格式：</h3>
          <div className="space-y-2 text-sm text-gray-600">
            <div className="bg-gray-50 p-2 rounded">yoursite.com/app/*</div>
            <div className="bg-gray-50 p-2 rounded">yoursite.com/share/*</div>
            <div className="bg-gray-50 p-2 rounded">yoursite.com/*</div>
          </div>
        </div>
      </div>
    </div>
  );
}
