'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import PerformanceOptimizer from '../components/PerformanceOptimizer';
import LogoImage from '../components/LogoImage';

export default function UniversalLinksPage() {
  const [isVisible, setIsVisible] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [appInstalled, setAppInstalled] = useState(false);

  useEffect(() => {
    // 页面加载动画
    const timer = requestAnimationFrame(() => {
      setIsVisible(true);
    });

    // 检测是否为iOS设备
    const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    setIsIOS(iOS);

    // 检测应用是否已安装（通过尝试打开应用链接）
    if (iOS) {
      const timeout = setTimeout(() => {
        setAppInstalled(false);
      }, 2000);

      // 如果应用打开成功，页面会失去焦点
      const handleVisibilityChange = () => {
        if (document.hidden) {
          setAppInstalled(true);
          clearTimeout(timeout);
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);

      return () => {
        clearTimeout(timeout);
        cancelAnimationFrame(timer);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    }

    return () => cancelAnimationFrame(timer);
  }, []);

  const handleOpenApp = () => {
    if (isIOS) {
      // 尝试打开应用
      window.location.href = 'protal://';

      // 如果应用未安装，2秒后跳转到App Store
      setTimeout(() => {
        if (!appInstalled) {
          window.location.href = 'https://apps.apple.com/app/your-app-id';
        }
      }, 2000);
    } else {
      alert('此功能仅支持iOS设备');
    }
  };

  const handleDownloadApp = () => {
    window.location.href = 'https://apps.apple.com/app/your-app-id';
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      <PerformanceOptimizer />

      {/* 背景装饰 - 与主页保持一致 */}
      <div className="absolute inset-0 bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50"></div>

      {/* 浮动装饰元素 */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-pink-300 to-purple-300 rounded-full opacity-20 anime-float gpu-accelerated"></div>
      <div className="absolute top-40 right-20 w-16 h-16 bg-gradient-to-r from-yellow-300 to-pink-300 rounded-full opacity-20 anime-float gpu-accelerated" style={{animationDelay: '1s'}}></div>
      <div className="absolute bottom-40 left-20 w-12 h-12 bg-gradient-to-r from-blue-300 to-purple-300 rounded-full opacity-20 anime-float gpu-accelerated" style={{animationDelay: '2s'}}></div>
      <div className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-pink-300 to-yellow-300 rounded-full opacity-20 anime-float gpu-accelerated" style={{animationDelay: '0.5s'}}></div>

      {/* 导航栏 */}
      <nav className="relative z-10 p-6">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <Link href="/" className={`flex items-center space-x-3 ${isVisible ? 'anime-bounce-in' : 'opacity-0'}`}>
            <div className="relative w-12 h-12 anime-float gpu-accelerated logo-ring">
              <LogoImage
                width={48}
                height={48}
                className="rounded-full object-cover logo-glow"
                priority
              />
            </div>
            <div className="text-3xl font-bold gradient-text">
              谷谷
            </div>
          </Link>
          <div className="hidden md:flex space-x-8">
            <Link href="/" className="text-gray-700 hover:text-[#FF6EB3] transition-colors font-medium">首页</Link>
            <Link href="/support" className="text-gray-700 hover:text-[#FF6EB3] transition-colors font-medium">技术支持</Link>
          </div>
        </div>
      </nav>

      {/* 主要内容区域 */}
      <main className="relative z-10 px-6 py-20">
        <div className="max-w-4xl mx-auto">
          {/* 标题区域 */}
          <div className={`text-center mb-16 gpu-accelerated ${isVisible ? 'anime-fade-in-up' : 'opacity-0'}`} style={{animationDelay: '0.1s'}}>
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className="gradient-text">Universal Links</span>
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              在iOS设备上获得无缝的应用体验，点击链接直接在谷谷应用中打开 ✨
            </p>
          </div>

          {/* 主要卡片区域 */}
          <div className={`glass-card rounded-3xl p-8 max-w-2xl mx-auto mb-12 gpu-accelerated ${isVisible ? 'anime-fade-in-up' : 'opacity-0'}`} style={{animationDelay: '0.3s'}}>
            <div className="text-center">
              {/* App图标区域 */}
              <div className="mb-8">
                <div className="relative w-24 h-24 mx-auto logo-ring mb-4">
                  <LogoImage
                    width={96}
                    height={96}
                    className="rounded-2xl object-cover kawaii-shadow logo-glow"
                  />
                  {/* 装饰性闪烁点 */}
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-300 rounded-full opacity-80 anime-sparkle"></div>
                  <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-pink-300 rounded-full opacity-80 anime-sparkle" style={{animationDelay: '0.5s'}}></div>
                </div>
                <h2 className="text-3xl font-bold gradient-text mb-2">谷谷</h2>
                <p className="text-gray-600">谷子管理助手</p>
              </div>

              {/* 设备检测和按钮区域 */}
              {isIOS ? (
                <div className="space-y-4">
                  <div className="p-4 bg-green-50 rounded-2xl border border-green-200">
                    <div className="text-2xl mb-2">📱</div>
                    <h3 className="font-bold text-green-800 mb-1">iOS设备已检测</h3>
                    <p className="text-sm text-green-700">
                      Universal Links功能已启用，点击下方按钮体验无缝跳转
                    </p>
                  </div>

                  <button
                    onClick={handleOpenApp}
                    className="w-full px-8 py-4 bg-gradient-to-r from-[#FF6EB3] to-[#6B73FF] text-white rounded-2xl font-bold text-lg kawaii-shadow hover-scale gpu-accelerated"
                  >
                    打开谷谷应用 ✨
                  </button>

                  <button
                    onClick={handleDownloadApp}
                    className="w-full px-8 py-4 glass-card text-gray-700 rounded-2xl font-bold text-lg hover-scale gpu-accelerated"
                  >
                    从 App Store 下载 📱
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="p-4 bg-yellow-50 rounded-2xl border border-yellow-200">
                    <div className="text-2xl mb-2">💻</div>
                    <h3 className="font-bold text-yellow-800 mb-1">非iOS设备</h3>
                    <p className="text-sm text-yellow-700">
                      请在iPhone或iPad上访问此页面以体验Universal Links功能
                    </p>
                  </div>

                  <button
                    onClick={() => window.open('https://apps.apple.com/app/your-app-id', '_blank')}
                    className="w-full px-8 py-4 bg-gradient-to-r from-[#FF6EB3] to-[#6B73FF] text-white rounded-2xl font-bold text-lg kawaii-shadow hover-scale gpu-accelerated"
                  >
                    在 App Store 中查看 🍎
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* 功能说明区域 */}
          <div className={`grid md:grid-cols-2 gap-6 mb-12 gpu-accelerated ${isVisible ? 'anime-fade-in-up' : 'opacity-0'}`} style={{animationDelay: '0.5s'}}>
            <div className="glass-card rounded-2xl p-6 hover-scale gpu-accelerated">
              <div className="text-4xl mb-4">🔗</div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">智能链接识别</h3>
              <p className="text-gray-600">
                当您在Safari中点击支持的链接时，系统会自动识别并在谷谷应用中打开，无需手动选择
              </p>
            </div>

            <div className="glass-card rounded-2xl p-6 hover-scale gpu-accelerated">
              <div className="text-4xl mb-4">⚡</div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">无缝体验</h3>
              <p className="text-gray-600">
                告别繁琐的应用切换，直接从网页链接跳转到应用内对应页面，体验更加流畅
              </p>
            </div>
          </div>

          {/* 支持的链接格式 */}
          <div className={`glass-card rounded-2xl p-6 gpu-accelerated ${isVisible ? 'anime-fade-in-up' : 'opacity-0'}`} style={{animationDelay: '0.7s'}}>
            <h3 className="text-xl font-bold text-gray-800 mb-4 text-center">
              <span className="gradient-text">支持的链接格式</span>
            </h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 p-3 bg-pink-50 rounded-xl">
                <div className="w-2 h-2 bg-pink-400 rounded-full"></div>
                <code className="text-sm text-gray-700 font-mono">yoursite.com/app/*</code>
                <span className="text-xs text-gray-500 ml-auto">应用相关页面</span>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-xl">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                <code className="text-sm text-gray-700 font-mono">yoursite.com/share/*</code>
                <span className="text-xs text-gray-500 ml-auto">分享相关页面</span>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-xl">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <code className="text-sm text-gray-700 font-mono">yoursite.com/*</code>
                <span className="text-xs text-gray-500 ml-auto">所有页面</span>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* 页脚 */}
      <footer className="relative z-10 bg-white/80 backdrop-blur-sm py-12">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="relative w-8 h-8">
              <LogoImage
                width={32}
                height={32}
                className="rounded-full object-cover"
              />
            </div>
            <div className="text-2xl font-bold gradient-text">谷谷</div>
          </div>
          <p className="text-gray-600 mb-6">让谷子管理变得更加有趣 ✨</p>
          <div className="flex justify-center space-x-8 text-gray-500">
            <Link
              href="/"
              className="hover:text-[#FF6EB3] transition-colors"
            >
              返回首页
            </Link>
            <Link
              href="/support"
              className="hover:text-[#FF6EB3] transition-colors"
            >
              技术支持
            </Link>
            <Link
              href="/privacy"
              className="hover:text-[#FF6EB3] transition-colors"
            >
              隐私政策
            </Link>
          </div>
          <div className="mt-8 text-sm text-gray-400 space-y-2">
            <div>© 2025 武汉菁耀科技有限公司. All rights reserved.</div>
            <div>
              <a
                href="https://beian.miit.gov.cn"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-[#FF6EB3] transition-colors"
              >
                鄂ICP备2023006067号-1
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
